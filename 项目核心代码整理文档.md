# 智能手表项目核心代码整理文档

## 项目概述

本项目是一个基于CH579芯片的智能手表系统，集成了辐射剂量检测、心率血氧监测、GPS定位、4G通信等功能。项目采用LVGL图形库构建用户界面，使用EC800M模块进行4G通信，支持MQTT协议数据上传。

## 项目架构

```
lyls-00021/
├── src/                    # 源代码目录
│   ├── main.c             # 主程序入口
│   ├── sys_it.c           # 中断服务程序
│   ├── system_set.c       # 系统配置管理
│   ├── diver/             # 驱动程序目录
│   ├── lvgl_user/         # LVGL用户界面
│   └── nanopb_user/       # Protobuf数据协议
├── lib/                   # 库文件目录
│   ├── CH579_Lib/         # CH579芯片库
│   ├── lvgl/              # LVGL图形库
│   └── nanopb/            # Nanopb协议库
└── obj/                   # 编译输出目录
```

## 1. 主程序模块 (main.c)

### 1.1 系统初始化流程

```c
int main()
{   
    system_wdog_init();                    // 看门狗初始化
    
    init_userkey();                        // 按键初始化
    if(R8_GLOB_RESET_KEEP == 0)waite_power_on_event();  // 等待开机事件
    system_state.watch_power_state = 1;
    
    // 外设初始化
    tmdelay_ms_init();                     // 延时定时器初始化
    uart0_init();                          // UART0初始化(EC800M通信)
    uart1_init();                          // UART1初始化(调试串口)
    uart2_init();                          // UART2初始化
    spi_flash_init();                      // SPI Flash初始化
    io_i2c_io_init();                      // I2C接口初始化
    user_sys_adc_init();                   // ADC初始化
    
    system_wdog_feed(0);
    pm_init();                             // 屏幕初始化
    pm_fill_color(0);                      // 屏幕填充
    system_wdog_feed(0);
    
    read_system_settings();                // 读取系统配置
    
    // LVGL图形库初始化
    lv_init();
    lv_port_disp_init();                   // 显示接口初始化
    lv_port_indev_init();                  // 输入设备初始化
    ui_init();                             // UI界面初始化
    
    // CSI传感器初始化
    csi_sensor_int();
    csi_sensor_collect_enable(ENABLE);
    system_wdog_feed(0);
    
    // 主循环
    while(1){
        user_display_updata();             // 屏幕与显示量的更新
        lv_task_handler();                 // LVGL库调度任务
        adc_scan_channel_task(...);        // ADC扫描任务
        at_state_machine_run(&default_ec800m_mc);  // DTU模块运行任务
        battery_reflash_task();            // 电池状态刷新
        auto_save_task();                  // 自动保存任务
        system_wdog_feed(0);               // 喂狗
    }   
}
```

### 1.2 主要功能说明

- **系统初始化**: 完成硬件外设、通信接口、图形库的初始化
- **主循环**: 采用轮询方式处理各个功能模块的任务
- **看门狗**: 防止系统死机，定期喂狗保证系统稳定运行

## 2. 中断服务程序 (sys_it.c)

### 2.1 定时器中断

```c
void TMR0_IRQHandler(void)        
{
    if(TMR0_GetITFlag(TMR0_3_IT_CYC_END))
    {
        TMR0_ClearITFlag(TMR0_3_IT_CYC_END);   
        
        uart_check_rxd_finish();          // UART接收完成检查
        tmdelay_tick_inc();                // 延时计数器递增
        lv_tick_inc(1);                    // LVGL时钟递增
    }
}
```

### 2.2 GPIO中断

```c
void GPIO_IRQHandler(void)
{
    chsc5816_tp_interrupt_handler();      // 触摸屏中断处理
    
    if(GPIOB_ReadITFlagBit(GPIO_Pin_14)){
        GPIOB_ClearITFlagBit(GPIO_Pin_14); // 清中断，PB14
        drc_pulse_interrupt_handler(&csi_sensor);  // CSI脉冲计数中断
    }
}
```

### 2.3 RTC中断

```c
void RTC_IRQHandler(void)    // 秒中断
{
    if(RTC_GetITFlag(RTC_TMR_EVENT)){
        RTC_ClearITFlag(RTC_TMR_EVENT);
        drc_second_interrupt_handler(&csi_sensor);  // CSI传感器秒中断处理
    }
}
```

## 3. 系统配置管理 (system_set.c)

### 3.1 系统配置结构体

```c
sys_set_t system_setings = {
    .sn = "LYWB01_A00000",                 // 设备序列号
    .param_hash = "default",               // 参数哈希值
    
    .bt_switch = 0,                        // 蓝牙开关
    .fly_mode_switch = 0,                  // 飞行模式开关
    .gnss_switch = 1,                      // GNSS开关
    .auto_upsend_switch = 1,               // 自动上传开关
    .auto_upsend_interval_envent = DEFAULT_SHORT_UPSEND_INTERVAL,
    .auto_upsend_interval_normal = DEFAULT_LONG_UPSEND_INTERVAL,
    .dose_rate_alarm_threshold = 2.0f,     // 剂量率报警阈值
    
    // CSI传感器拟合参数
    .csi_dr_fitting_param = {
        {0, 6.8f, 0, 0.3235f, 0},
        {6.8, 21.7f, 0, 0.442f, -0.8f},
        {21.7f, 92.0f, 0, 0.4523f, -1.0159f},
        {92.0f, 186.6f, 0, 0.463f, -1.996f},
        {186.6f, 634.0f, 0, 0.303f, 27.8f},
    },
    .csi_dr_fitting_param_num = 5,
    .csi_alarm_dose_rate_max = 60.0f,      // CSI报警剂量率上限
    .csi_alarm_dose_rate_min = 0,          // CSI报警剂量率下限
    .csi_alarm_dose_rate_sum = 0,          // CSI累计剂量报警阈值
};
```

### 3.2 配置读取与保存

```c
void read_system_settings(void)
{
    sys_setting_save_t read_save_t;
    spi_flash_read_data(0x00, (uint8_t *)&read_save_t, sizeof(sys_setting_save_t));
    
    if(read_save_t.init_flag != SYS_SETTING_SAVE_INIT_FLAG){
        system_settings_saved();          // 初始化保存
    }
    else{
        // 校验和检查，选择有效配置
        uint16_t main_check_sum = 0;
        uint16_t backup_check_sum = 0;
        // ... 校验逻辑
        memcpy(&system_setings, effective_seting, sizeof(sys_set_t));
    }
}
```

## 4. 驱动程序模块

### 4.1 CSI传感器驱动 (csi_sensor.c)

CSI传感器用于辐射剂量检测，是项目的核心功能之一。

```c
// CSI传感器对象
drc_sensor_obj_t csi_sensor;

// 传感器初始化
void csi_sensor_int(void)
{
    memset(&csi_sensor, 0, sizeof(csi_sensor));
    
    // 注册API接口
    csi_sensor.api.pin_init = csi_sensor_pin_init;
    csi_sensor.api.second_interrupt_init = urtc_second_irq_switch;
    csi_sensor.api.sensor_pw_switch = csi_sensor_power;
    
    // 设置拟合与报警参数
    memcpy(csi_sensor.fitting_param, system_setings.csi_dr_fitting_param, 
           sizeof(piecewise_fitting_param_t) * system_setings.csi_dr_fitting_param_num);
    csi_sensor.fitting_param_num = system_setings.csi_dr_fitting_param_num;
    csi_sensor.alarm_param_max_dose_rate = system_setings.csi_alarm_dose_rate_max;
    
    // 设置跳变检测算法参数
    drc_set_jpavg_param(&csi_sensor, 3.0f, 10.0f, 3, 10, 10);
}

// 电源控制
void csi_sensor_power(uint8_t en)
{
    if(en){
        GPIOB_SetBits(GPIO_Pin_13);        // 开启电源
    }
    else{
        GPIOB_ResetBits(GPIO_Pin_13);      // 关闭电源
    }
}
```

### 4.2 剂量率计数器 (dose_rate_counter.c)

实现辐射剂量的计算和跳变检测算法。

```c
// 脉冲中断处理函数
void drc_pulse_interrupt_handler(drc_sensor_obj_t * obj)
{
    obj->cnt_temp++;                       // 脉冲计数累加
}

// 秒中断处理函数
void drc_second_interrupt_handler(drc_sensor_obj_t *obj)
{
    obj->cnt_now = obj->cnt_temp;          // 获取当前脉冲计数值
    obj->cnt_temp = 0;                     // 清零，得到每秒计数值CPS
    
    if(obj->power_statu){                  // 传感器开启状态
        if(obj->re_start){                 // 丢弃第一秒数据
            obj->re_start = 0;					
        }   
        else{
            // 跳变检测
            uint8_t pulse_jump_flag = JPAVG_AddDataToBuffer(obj->cnt_now, &obj->jump_averager);
            obj->cps = JPAVG_GetDataStableAverage(&obj->jump_averager);
            
            if(pulse_jump_flag != 0){      // 有跳变
                obj->re_start = 1;
                JPAVG_DataBufferClean(&obj->jump_averager);
            }  
            else{                          // 无跳变，进行剂量率计算
                float x = obj->cps;
                uint32_t index = 0;
                
                // 查找匹配的拟合参数区间
                for(index = 0; index < obj->fitting_param_num; index++){
                    if((x >= obj->fitting_param[index].cps_start) && 
                       (x <= obj->fitting_param[index].cps_end)){
                        break;
                    }
                }
                
                // 二次拟合计算剂量率
                float a = obj->fitting_param[index].param_a;
                float b = obj->fitting_param[index].param_b;
                float c = obj->fitting_param[index].param_c;
                obj->dose_rate = x * x * a + x * b + c;
                
                // 报警检测
                obj->alarm = 0;
                if((obj->alarm_param_max_dose_rate > 0) && 
                   (obj->dose_rate >= obj->alarm_param_max_dose_rate)){
                    obj->alarm |= (1 << 1);    // 剂量率过高报警
                }
                
                // 累计剂量计算
                if(JPAVG_GetStableFiledNum(&obj->jump_averager) > 
                   obj->jump_averager.jump_check_start_data_num){
                    obj->dose_sum += obj->dose_rate/3600;  // 累计剂量
                }
            }
        }
    }
}
```

### 4.3 跳变检测算法 (jump_averager.c)

实现基于统计学的跳变检测算法，用于识别辐射环境的突变。

```c
// 添加数据到缓存并进行跳变检测
uint8_t JPAVG_AddDataToBuffer(DATA_TYPE data, jump_averager_obj_t * obj)
{
    if(obj->last_data_buffer_filed_num >= obj->param_out_cgm_1_t){
        // 将"最近数组"中最老的数据填入原始数据缓存
        DATA_TYPE save_data = obj->last_data_buffer[obj->last_data_buffer_index];
        obj->data_buffer_sum -= obj->data_buffer[obj->data_buffer_index];
        obj->data_buffer[obj->data_buffer_index] = save_data;

        // 更新统计参数
        obj->data_buffer_sum += save_data;
        obj->stable_data_average = obj->data_buffer_sum / obj->data_buffer_filed_num;

        // 计算方差
        double variance_sum = 0;
        for(uint32_t i = 0; i < obj->data_buffer_filed_num; i++){
            double diff = obj->data_buffer[i] - obj->stable_data_average;
            variance_sum += diff * diff;
        }
        obj->stable_data_average_variance = sqrt(variance_sum / obj->data_buffer_filed_num);

        if(obj->data_buffer_filed_num >= obj->jump_check_start_data_num){
            double difference = fabs(data - obj->stable_data_average);
            double n_cgm_th_1 = obj->param_cgm_1 * obj->stable_data_average_variance;  // 精确阈值
            double n_cgm_th_2 = obj->param_cgm_2 * obj->stable_data_average_variance;  // 粗阈值

            // 3σ跳变检测
            if(difference >= n_cgm_th_2){
                obj->jump_flag = 1;        // 粗阈值跳变
            }
            else if(difference >= n_cgm_th_1){
                obj->out_difference_times++;
                if(obj->out_difference_times >= obj->param_out_cgm_1_t){
                    obj->jump_flag = 2;    // 连续超出精确阈值
                }
            }
            else{
                obj->out_difference_times = 0;
            }

            // 持续增减法检测
            if((obj->stable_data_average - obj->last_data_average) > 0.001f){
                obj->increase_times++;
                obj->decrease_times = 0;
            }
            else if((obj->last_data_average - obj->stable_data_average) > 0.001f){
                obj->decrease_times++;
                obj->increase_times = 0;
            }

            // 连续变化检测
            if((obj->increase_times >= obj->param_ct_inodc_t) ||
               (obj->decrease_times >= obj->param_ct_inodc_t)){
                obj->jump_flag = 3;        // 连续增减跳变
            }
        }
    }

    // 更新最近数据缓存
    obj->last_data_buffer[obj->last_data_buffer_index] = data;
    obj->last_data_buffer_index = (obj->last_data_buffer_index + 1) % LAST_DATA_BUFFER_LEN;

    uint8_t return_flag = obj->jump_flag;
    obj->jump_flag = 0;
    return return_flag;
}
```

### 4.4 电池管理 (battery.c)

实现电池电量检测和充电状态监控。

```c
// 电池电量检测
uint8_t get_battery(uint8_t * charger)
{
    if(!sys_adc_channel_table[0].result_ready) return 0;

    // 从ADC读取电压值
    float dev_voltage = sys_adc_channel_table[0].voltage;

    // 计算实际电池电压 (电阻分压: 100k/(100k+30k))
    float bt_voltage = (dev_voltage / 100000) * 130000;

    // 电池电量百分比计算 (2.8V-4.1V)
    int8_t battery_prt = ((bt_voltage - 2.8f) / (4.1f - 2.8f)) * 100;
    battery_prt = battery_prt > 100 ? 100 : battery_prt;
    battery_prt = battery_prt < 0 ? 0 : battery_prt;

    // 电量变化平滑处理
    static uint8_t last_prt = 0xff;
    static uint8_t zhong_jian_prt = 0xff;
    static uint32_t changed_delay = 0;

    if(last_prt == 0xff){
        last_prt = battery_prt;
        zhong_jian_prt = battery_prt;
    }

    if(last_prt != battery_prt){
        changed_delay++;
    }
    else{
        changed_delay = 0;
    }

    // 持续变化100次后更新电量值
    if(changed_delay >= 100){
        last_prt = zhong_jian_prt;
        changed_delay = 0;
    }
    else if(changed_delay == 50){
        zhong_jian_prt = battery_prt;
    }

    // 充电器状态检测
    *charger = 0;
    GPIOA_ModeCfg(GPIO_Pin_10, GPIO_ModeIN_Floating);
    if(GPIOA_ReadPortPin(GPIO_Pin_10)){
        *charger = 1;  // 充电器已插入
    }

    return last_prt;
}
```

### 4.5 按键处理 (userkey.c)

实现按键的短按、长按检测。

```c
// 按键扫描函数
uint8_t sacn_userkey(void)
{
    static uint32_t t = 0;
    static uint8_t step = 0;             // 状态机: 0-检测按下; 1-检测松手; 2-返回事件
    static uint32_t t_continue = 0;      // 长按计时器

    // 长按检测
    if(get_userkey() == 0){              // 按键按下
        if(t_continue++ >= PRESSED_LONG_TIME){
            step = 0;
            t = 0;
            t_continue = 0;
            return 2;                    // 返回长按事件
        }
    }
    else{
        t_continue = 0;
    }

    // 短按检测状态机
    switch (step)
    {
        case 0: // 监测是否按下
        {
            if(get_userkey() == 0){
                t++;
            }
            else{
                t = 0;
            }

            if(t >= PRESSED_SHORT_TIME){
                t = 0;
                step = 1;                // 进入检测松手状态
            }
        }
        break;

        case 1: // 监测是否松手
        {
            if(get_userkey() == 1){
                t++;
            }
            else{
                t = 0;
            }

            if(t >= 100){
                t = 0;
                step = 2;                // 进入返回事件状态
            }
        }
        break;

        case 2: // 返回按键事件
        {
            step = 0;
            return 1;                    // 返回短按事件
        }

        default:
            break;
    }

    return 0;                            // 无事件
}
```

## 5. 通信模块

### 5.1 EC800M 4G通信模块 (ec800m.c)

实现4G网络通信和MQTT数据上传功能。

```c
// EC800M状态枚举
enum ec800m_state
{
    // 初始化状态序列
    POWER_OFF_ing, POWER_OFF, POWER_ON_ing, REDY,
    GET_IMEI, GET_ICCID, SET_CREG, SET_GPS_APFLASH,
    CEK_AGPS, SET_AGPS, OPEN_GPS, MQTT_COFIG,
    MQTT_OPEN, MQTT_CONNECT, MQTT_SUB,

    // 空闲任务
    IDEL,

    // 事件触发状态
    GET_CREG, GET_TIME, GET_CSQ, GET_GNSS,
    MQTT_REQUEST_PUB, MQTT_PUB_HEX,
};

// EC800M消息结构体
typedef struct
{
    uint8_t upsend_state;                // 上报状态: 0-空闲; 1-正在上报; 2-成功; 3-失败
    uint8_t new_data_redy;               // 上报数据就绪标志
    uint8_t * new_data;                  // 需要上报的数据
    uint16_t new_data_length;            // 数据长度
    unix_time_t last_upsend_time;        // 上次上报时间
    uint8_t imei[16];                    // 设备IMEI
    uint8_t iccid[21];                   // SIM卡ICCID
    uint16_t lac;                        // 位置区码
    uint32_t cid;                        // 小区ID
    uint8_t csq;                         // 信号强度
    uint8_t gnss_star_num;               // 卫星数量
    double gnss_latitude;                // 纬度
    double gnss_longitude;               // 经度
    uint16_t gnss_altitude;              // 海拔
}ec800m_msg_t;

// MQTT发布请求
uint8_t _request_pub(uint8_t * next_state)
{
    uint8_t mqtt_pub_cmd[90];
    memset(mqtt_pub_cmd, 0, sizeof(mqtt_pub_cmd));
    sprintf((char *)mqtt_pub_cmd, "AT+QMTPUBEX=0,0,0,0,\"/iot/%s/pb/datapoint/%s\",%d\r\n",
            MQTT_DEVICE_ID, default_ec800m.imei, default_ec800m.new_data_length);
    default_ec800m_mc.command_send(mqtt_pub_cmd, strlen((char *)mqtt_pub_cmd));
    default_ec800m.upsend_state = 1;
    return 0;
}

// 空闲事件检查
uint8_t _idel_check_envent(uint8_t * next_state)
{
    static uint32_t get_time_time = 0;
    static uint32_t get_creg_time = 0;
    static uint32_t get_csq_time = 0;
    static uint32_t get_gnss_time = 0;
    static unix_time_t idle_time = 0;

    // 定期获取网络时间
    if((GET_MS_TICK() - get_time_time >= 3600000) || (get_time_time == 0)){
        get_time_time = GET_MS_TICK();
        * next_state = GET_TIME;
        return 1;
    }

    // 定期获取网络注册状态
    if((GET_MS_TICK() - get_creg_time >= 60000) || (get_creg_time == 0)){
        get_creg_time = GET_MS_TICK();
        * next_state = GET_CREG;
        return 1;
    }

    // 定期获取信号强度
    if((GET_MS_TICK() - get_csq_time >= 30000) || (get_csq_time == 0)){
        get_csq_time = GET_MS_TICK();
        * next_state = GET_CSQ;
        return 1;
    }

    // 定期获取GNSS定位
    if((GET_MS_TICK() - get_gnss_time >= 5000) || (get_gnss_time == 0)){
        get_gnss_time = GET_MS_TICK();
        * next_state = GET_GNSS;
        return 1;
    }

    // 检查数据上传事件
    check_upsend_envent();
    if(default_ec800m.new_data_redy){
        * next_state = MQTT_REQUEST_PUB;
        idle_time = 0;
        return 1;
    }

    // 空闲超时关机
    if(idle_time == 0){
        idle_time = sys_unix_time;
    }
    else if((sys_unix_time - idle_time) >= 180){
        * next_state = POWER_OFF_ing;
        idle_time = 0;
        return 1;
    }

    // 飞行模式检查
    if(system_setings.fly_mode_switch){
        * next_state = POWER_OFF_ing;
        return 1;
    }

    return 0;
}
```

### 5.2 AT状态机 (at_state_machine.c)

实现AT命令的状态机管理。

```c
// 状态机运行函数
void at_state_machine_run(at_state_machine_t * state_machine)
{
    uint8_t now_statu = state_machine->now_state;
    static uint16_t last_statu = 0xFFFF;

    // 状态切换时清理临时变量
    if(last_statu != now_statu && last_statu != 0xFFFF){
        state_machine->state_table[last_statu].re_try_times_temp = 0;
        state_machine->state_table[last_statu].time_temp = 0;
    }
    last_statu = now_statu;

    // URC消息检查
    if(*state_machine->rxd_message_finished == 1){
        for(uint16_t i = 0; i < state_machine->urc_care_table_length; i++){
            if(strstr((char*)state_machine->rxd_message_buffer,
                     (char*)state_machine->urc_care_table[i].urc_key) != NULL){
                uint8_t next_state;
                if(state_machine->urc_care_table[i].urc_callback(
                   state_machine->rxd_message_buffer, &next_state)){
                    state_machine->now_state = next_state;
                    _clear_state_machine_rxd_buffer(state_machine);
                    return;
                }
            }
        }
    }

    // 应答检查
    if(*state_machine->rxd_message_finished == 1){
        if(state_machine->state_table[now_statu].wait_ack != NULL){
            if(strstr((char*)state_machine->rxd_message_buffer,
                     (char*)state_machine->state_table[now_statu].wait_ack) != NULL){
                if(state_machine->state_table[now_statu].ack_callback != NULL){
                    uint8_t next_state;
                    if(state_machine->state_table[now_statu].ack_callback(
                       state_machine->rxd_message_buffer, &next_state)){
                        state_machine->now_state = next_state;
                        _clear_state_machine_rxd_buffer(state_machine);
                        return;
                    }
                }
            }
        }
    }

    // 动作发起和超时处理
    if(0 == state_machine->state_table[now_statu].time_temp){
        if(state_machine->state_table[now_statu].onece_time_out != NO_TIMEOUT){
            state_machine->state_table[now_statu].time_temp = GET_MS_TICK();
        }

        // 超时检查
        if(state_machine->state_table[now_statu].re_try_times ==
           state_machine->state_table[now_statu].re_try_times_temp){
            if(state_machine->state_table[now_statu].timeout_callback != NULL){
                uint8_t next_state;
                if(state_machine->state_table[now_statu].timeout_callback(&next_state)){
                    state_machine->now_state = next_state;
                    _clear_state_machine_rxd_buffer(state_machine);
                    return;
                }
            }
        }
        else{
            // 发送AT命令
            if(state_machine->state_table[now_statu].at_cmd != NULL){
                state_machine->command_send(state_machine->state_table[now_statu].at_cmd,
                                          strlen((char*)state_machine->state_table[now_statu].at_cmd));
            }

            // 执行动作回调
            if(state_machine->state_table[now_statu].action_callback != NULL){
                uint8_t next_state;
                if(state_machine->state_table[now_statu].action_callback(&next_state)){
                    state_machine->now_state = next_state;
                    _clear_state_machine_rxd_buffer(state_machine);
                    return;
                }
            }
        }
    }

    // 超时处理
    if((state_machine->state_table[now_statu].onece_time_out != NO_TIMEOUT) &&
       (GET_MS_TICK() - state_machine->state_table[now_statu].time_temp >=
        state_machine->state_table[now_statu].onece_time_out)){
        state_machine->state_table[now_statu].time_temp = 0;
        state_machine->state_table[now_statu].re_try_times_temp++;
    }
}
```

## 6. 用户界面模块

### 6.1 LVGL移植 (lv_port_disp.c)

```c
// 显示接口初始化
void lv_port_disp_init(void)
{
    static lv_disp_draw_buf_t draw_buf_dsc_1;
    static lv_color_t draw_buf_1[MY_DISP_HOR_RES * 10];
    lv_disp_draw_buf_init(&draw_buf_dsc_1, draw_buf_1, NULL, MY_DISP_HOR_RES * 10);

    static lv_disp_drv_t disp_drv;
    lv_disp_drv_init(&disp_drv);
    disp_drv.hor_res = MY_DISP_HOR_RES;
    disp_drv.ver_res = MY_DISP_VER_RES;
    disp_drv.flush_cb = disp_flush;
    disp_drv.draw_buf = &draw_buf_dsc_1;
    lv_disp_drv_register(&disp_drv);
}

// 显示刷新回调
static void disp_flush(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
    pm_fill_area(area->x1, area->y1, area->x2, area->y2, (uint8_t*)color_p);
    lv_disp_flush_ready(disp_drv);
}
```

### 6.2 触摸输入接口 (lv_port_indev.c)

```c
// 输入设备初始化
void lv_port_indev_init(void)
{
    touchpad_init();

    static lv_indev_drv_t indev_drv;
    lv_indev_drv_init(&indev_drv);
    indev_drv.type = LV_INDEV_TYPE_POINTER;
    indev_drv.read_cb = pm_tp_point_read;
    indev_touchpad = lv_indev_drv_register(&indev_drv);
}

// 触摸点读取
void pm_tp_point_read(lv_indev_drv_t * indev_drv, lv_indev_data_t * data)
{
    uint16_t x, y;
    uint8_t state;
    chsc5816_tp_point_read(&x, &y, &state);
    data->point.x = x;
    data->point.y = y;
    data->state = (state) ? LV_INDEV_STATE_PRESSED : LV_INDEV_STATE_RELEASED;
    pm_touch_state = state;
}
```

### 6.3 UI界面管理 (ui.c)

```c
// UI初始化
void ui_init(void)
{
    lv_disp_t * dispp = lv_disp_get_default();
    lv_theme_t * theme = lv_theme_default_init(dispp,
                                               lv_palette_main(LV_PALETTE_BLUE),
                                               lv_palette_main(LV_PALETTE_RED),
                                               true, LV_FONT_DEFAULT);
    lv_disp_set_theme(dispp, theme);
    ui_StartScreen_screen_init();
    lv_disp_load_scr(ui_StartScreen);
}

// 主界面事件处理
void ui_event_HomeScreen(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);

    if(event_code == LV_EVENT_GESTURE &&
       lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
        lv_indev_wait_release(lv_indev_get_act());
        lv_obj_del_async(ui_HomeScreen);
        ui_SysSetingScreen_screen_init();
        _ui_screen_change(&ui_SysSetingScreen, LV_SCR_LOAD_ANIM_NONE, 0, 0,
                         &ui_SysSetingScreen_screen_init);
    }
}

// 主界面初始化
void ui_HomeScreen_screen_init(void)
{
    beijing_time_t t;
    get_beijing_time(&t, sys_unix_time);

    ui_HomeScreen = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_HomeScreen, LV_OBJ_FLAG_SCROLLABLE);

    // 时间显示面板
    ui_TimePanel = lv_obj_create(ui_HomeScreen);
    lv_obj_set_width(ui_TimePanel, 320);
    lv_obj_set_height(ui_TimePanel, 80);
    lv_obj_set_align(ui_TimePanel, LV_ALIGN_TOP_MID);

    // 小时标签
    ui_HourLable = lv_label_create(ui_TimePanel);
    lv_obj_set_align(ui_HourLable, LV_ALIGN_LEFT_MID);
    lv_label_set_text_fmt(ui_HourLable, "%02d", t.hour);
    lv_obj_set_style_text_font(ui_HourLable, &ui_font_pjfont70, LV_PART_MAIN | LV_STATE_DEFAULT);

    // 分钟标签
    ui_MinuLable = lv_label_create(ui_TimePanel);
    lv_obj_set_align(ui_MinuLable, LV_ALIGN_RIGHT_MID);
    lv_label_set_text_fmt(ui_MinuLable, "%02d", t.minute);
    lv_obj_set_style_text_font(ui_MinuLable, &ui_font_pjfont70, LV_PART_MAIN | LV_STATE_DEFAULT);

    // 剂量率显示面板
    ui_DoseRatePanel = lv_obj_create(ui_HomeScreen);
    lv_obj_set_width(ui_DoseRatePanel, 320);
    lv_obj_set_height(ui_DoseRatePanel, 71);
    lv_obj_set_align(ui_DoseRatePanel, LV_ALIGN_CENTER);

    ui_DoseRateLable = lv_label_create(ui_DoseRatePanel);
    lv_obj_set_align(ui_DoseRateLable, LV_ALIGN_CENTER);
    char dose_rate_string[20];
    sprintf(dose_rate_string,"%0.2f", csi_sensor.dose_rate);
    lv_label_set_text(ui_DoseRateLable, dose_rate_string);
    lv_obj_set_style_text_color(ui_DoseRateLable, lv_color_hex(0xFFB600), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_DoseRateLable, &ui_font_pjXiFont40, LV_PART_MAIN | LV_STATE_DEFAULT);

    // 添加事件处理
    lv_obj_add_event_cb(ui_HomeScreen, ui_event_HomeScreen, LV_EVENT_ALL, NULL);
}
```

### 6.4 显示数据更新 (user_display.c)

```c
// 显示数据更新函数
void user_display_updata(void)
{
    static uint32_t mp_t = 0;

    // 开机画面切换逻辑
    if(system_state.watch_power_state == 2){
        if(get_userkey()==0){              // 等待开机键释放后再切换画面
            return;
        }
        ui_HomeScreen_screen_init();
        _ui_screen_change(&ui_HomeScreen, LV_SCR_LOAD_ANIM_NONE, 0, 1000, &ui_HomeScreen_screen_init);
        system_state.watch_power_state = 3;
    }

    // 主界面数据更新
    if(lv_scr_act() == ui_HomeScreen){
        update_home_screen_data();
    }

    // 设置界面数据更新
    if(lv_scr_act() == ui_SysSetingScreen){
        update_setting_screen_data();
    }
}

// 主界面数据更新
void update_home_screen_data(void)
{
    // 更新时间显示
    static uint32_t last_minute = 0xFFFFFFFF;
    beijing_time_t t;
    get_beijing_time(&t, sys_unix_time);
    uint32_t now_minute = t.hour * 60 + t.minute;

    if(last_minute != now_minute){
        last_minute = now_minute;
        lv_label_set_text_fmt(ui_HourLable, "%02d", t.hour);
        lv_label_set_text_fmt(ui_MinuLable, "%02d", t.minute);
        lv_label_set_text_fmt(ui_DateLable, "%02d/%02d", t.month, t.day);
    }

    // 更新剂量率显示
    static float last_dose_rate = 9999999.66f;
    if(last_dose_rate != csi_sensor.dose_rate){
        last_dose_rate = csi_sensor.dose_rate;
        char dose_rate_string[20];
        sprintf(dose_rate_string,"%0.2f", csi_sensor.dose_rate);
        lv_label_set_text(ui_DoseRateLable, dose_rate_string);
    }

    // 更新电量显示
    static uint8_t last_bt_prt = 0;
    if(dev_battery_prt != last_bt_prt){
        last_bt_prt = dev_battery_prt;
        lv_label_set_text_fmt(ui_BatLable, "%d%%", last_bt_prt);
        lv_color_t bt_color = last_bt_prt > 10 ? lv_color_hex(0x24B600) : lv_color_hex(0xFF0000);
        lv_obj_set_style_text_color(ui_BatLable, bt_color, LV_PART_MAIN | LV_STATE_DEFAULT);
    }
}
```

## 7. 数据协议模块

### 7.1 Protobuf数据结构 (wb01_message.proto)

```protobuf
// 上报数据点
message data_point_t{
    required string sn = 1 [(nanopb).max_length = 13];              // 设备序列号
    required string firmware_version = 2 [(nanopb).max_length = 17]; // 固件版本号
    required string param_hash = 3 [(nanopb).max_length = 65];       // 参数哈希值
    required uint64 timestamp = 4;                                   // Unix时间戳

    optional dtu_t dtu = 5;                                          // DTU信息
    optional gnss_t gnss = 6;                                        // GNSS定位信息
    optional dose_rate_t dose_rate = 7;                              // 剂量率信息
    optional power_battery_t power_battery = 8;                      // 电池信息
}

// 剂量率数据
message dose_rate_t{
    required float dose_rate = 1;                                    // 当前剂量率
    required float dose_sum = 2;                                     // 累计剂量
    required float cps = 3;                                          // 计数率
    required uint32 cnt = 4;                                         // 脉冲计数
    required uint32 alarm = 5;                                       // 报警状态
}

// GNSS定位数据
message gnss_t{
    required double latitude = 1;                                    // 纬度
    required double longitude = 2;                                   // 经度
    required uint32 altitude = 3;                                    // 海拔
    required uint32 star_num = 4;                                    // 卫星数量
}

// DTU通信模块信息
message dtu_t{
    required string imei = 1 [(nanopb).max_length = 16];            // 设备IMEI
    required string iccid = 2 [(nanopb).max_length = 21];           // SIM卡ICCID
    required uint32 lac = 3;                                         // 位置区码
    required uint32 cid = 4;                                         // 小区ID
    required uint32 csq = 5;                                         // 信号强度
}

// 电池电源信息
message power_battery_t{
    required uint32 battery_prt = 1;                                 // 电池电量百分比
    required uint32 charger_state = 2;                              // 充电器状态
}
```

### 7.2 数据编码处理 (analytical_pb.c)

```c
// 编码数据点信息
uint16_t encode_data_point(uint8_t * output_buffer, uint16_t buffer_size)
{
    wb01_message_data_point_t data_point = wb01_message_data_point_t_init_zero;

    // 填充基本信息
    strcpy(data_point.sn, system_setings.sn);
    strcpy(data_point.firmware_version, FIRMWARE_VERSION);
    strcpy(data_point.param_hash, system_setings.param_hash);
    data_point.timestamp = sys_unix_time;

    // 填充DTU信息
    data_point.has_dtu = true;
    strcpy(data_point.dtu.imei, (char*)default_ec800m.imei);
    strcpy(data_point.dtu.iccid, (char*)default_ec800m.iccid);
    data_point.dtu.lac = default_ec800m.lac;
    data_point.dtu.cid = default_ec800m.cid;
    data_point.dtu.csq = default_ec800m.csq;

    // 填充GNSS信息
    data_point.has_gnss = true;
    data_point.gnss.latitude = default_ec800m.gnss_latitude;
    data_point.gnss.longitude = default_ec800m.gnss_longitude;
    data_point.gnss.altitude = default_ec800m.gnss_altitude;
    data_point.gnss.star_num = default_ec800m.gnss_star_num;

    // 填充剂量率信息
    data_point.has_dose_rate = true;
    data_point.dose_rate.dose_rate = csi_sensor.dose_rate;
    data_point.dose_rate.dose_sum = csi_sensor.dose_sum;
    data_point.dose_rate.cps = csi_sensor.cps;
    data_point.dose_rate.cnt = csi_sensor.cnt_now;
    data_point.dose_rate.alarm = csi_sensor.alarm;

    // 填充电池信息
    data_point.has_power_battery = true;
    data_point.power_battery.battery_prt = dev_battery_prt;
    data_point.power_battery.charger_state = charger_state;

    // 编码
    pb_ostream_t stream = pb_ostream_from_buffer(output_buffer, buffer_size);
    bool status = pb_encode(&stream, wb01_message_data_point_t_fields, &data_point);

    if(!status){
        return 0;
    }

    return stream.bytes_written;
}

// 数据上传检查
void check_upsend_envent(void)
{
    static unix_time_t last_check_time = 0;

    if(sys_unix_time - last_check_time >= 10){  // 每10秒检查一次
        last_check_time = sys_unix_time;

        // 检查是否需要上传数据
        if(system_setings.auto_upsend_switch){
            unix_time_t interval = system_setings.auto_upsend_interval_normal;

            // 如果有报警事件，使用短间隔
            if(csi_sensor.alarm != 0){
                interval = system_setings.auto_upsend_interval_envent;
            }

            if((sys_unix_time - default_ec800m.last_upsend_time) >= interval){
                // 编码数据
                uint8_t pb_buffer[512];
                uint16_t pb_length = encode_data_point(pb_buffer, sizeof(pb_buffer));

                if(pb_length > 0){
                    default_ec800m.new_data = pb_buffer;
                    default_ec800m.new_data_length = pb_length;
                    default_ec800m.new_data_redy = 1;
                    default_ec800m.last_upsend_time = sys_unix_time;
                }
            }
        }
    }
}
```

## 8. 外设驱动模块

### 8.1 显示屏驱动 (icna3311.c)

```c
// 显示屏初始化命令
void icna3311_init_cmd(uint16_t start_column, uint16_t end_column,
                       uint16_t start_row, uint16_t end_row)
{
    // 软复位
    icna3311_write_4wspi(0x01, NULL, 0);
    DelayMs(120);

    // 退出睡眠模式
    icna3311_write_4wspi(0x11, NULL, 0);
    DelayMs(120);

    // 设置显示区域
    uint8_t column_data[4] = {start_column >> 8, start_column & 0xFF,
                              end_column >> 8, end_column & 0xFF};
    icna3311_write_4wspi(0x2A, column_data, 4);

    uint8_t row_data[4] = {start_row >> 8, start_row & 0xFF,
                           end_row >> 8, end_row & 0xFF};
    icna3311_write_4wspi(0x2B, row_data, 4);

    // 开启显示
    icna3311_write_4wspi(0x29, NULL, 0);
    DelayMs(50);
}

// SPI写入函数
void icna3311_write_4wspi(uint8_t cmd, uint8_t * data, uint16_t data_len)
{
    ICNA3311_CS_0;                       // 片选拉低

    ICNA3311_DC_0;                       // 命令模式
    SPI1_MasterSendByte(cmd);            // 发送命令

    if(data_len > 0){
        ICNA3311_DC_1;                   // 数据模式
        for(uint16_t i = 0; i < data_len; i++){
            SPI1_MasterSendByte(data[i]); // 发送数据
        }
    }

    ICNA3311_CS_1;                       // 片选拉高
}

// 区域填充函数
void pm_fill_area(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint8_t * color_data)
{
    uint32_t pixel_count = (x2 - x1 + 1) * (y2 - y1 + 1);

    // 设置显示窗口
    uint8_t column_data[4] = {x1 >> 8, x1 & 0xFF, x2 >> 8, x2 & 0xFF};
    icna3311_write_4wspi(0x2A, column_data, 4);

    uint8_t row_data[4] = {y1 >> 8, y1 & 0xFF, y2 >> 8, y2 & 0xFF};
    icna3311_write_4wspi(0x2B, row_data, 4);

    // 开始写入像素数据
    ICNA3311_CS_0;
    ICNA3311_DC_0;
    SPI1_MasterSendByte(0x2C);           // 写入内存命令

    ICNA3311_DC_1;                       // 数据模式
    for(uint32_t i = 0; i < pixel_count; i++){
        SPI1_MasterSendByte(color_data[i]);
    }

    ICNA3311_CS_1;
}
```

### 8.10 蓝牙BLE驱动 (peripheral.c)

```c
// BLE外设初始化
void Peripheral_Init( )
{
    Peripheral_TaskID = TMOS_ProcessEventRegister( Peripheral_ProcessEvent );

    // 设置GAP参数
    {
        uint16 advInt = DEFAULT_ADVERTISING_INTERVAL;
        uint8 enableUpdateRequest = DEFAULT_ENABLE_UPDATE_REQUEST;
        uint16 desiredMinInterval = DEFAULT_DESIRED_MIN_CONN_INTERVAL;
        uint16 desiredMaxInterval = DEFAULT_DESIRED_MAX_CONN_INTERVAL;
        uint16 desiredSlaveLatency = DEFAULT_DESIRED_SLAVE_LATENCY;
        uint16 desiredConnTimeout = DEFAULT_DESIRED_CONN_TIMEOUT;

        GAPRole_SetParameter( GAPROLE_ADVERT_ENABLED, sizeof( uint8 ), &initial_advertising_enable );
        GAPRole_SetParameter( GAPROLE_ADVERT_OFF_TIME, sizeof( uint16 ), &gapRole_AdvertOffTime );
        GAPRole_SetParameter( GAPROLE_SCAN_RSP_DATA, sizeof ( scanRspData ), scanRspData );
        GAPRole_SetParameter( GAPROLE_ADVERT_DATA, sizeof( advertData ), advertData );
        GAPRole_SetParameter( GAPROLE_ADV_EVENT_TYPE, sizeof( uint8 ), &advType );
        GAPRole_SetParameter( GAPROLE_ADV_DIRECT_TYPE, sizeof( uint8 ), &advDirectType );
        GAPRole_SetParameter( GAPROLE_ADV_CHANNEL_MAP, sizeof( uint8 ), &advChanMap );
        GAPRole_SetParameter( GAPROLE_ADV_FILTER_POLICY, sizeof( uint8 ), &advFilterPolicy );
    }

    // 设置GAP绑定管理器
    {
        uint32 passkey = 0;                                   // 配对密码 "000000"
        uint8 pairMode = GAPBOND_PAIRING_MODE_WAIT_FOR_REQ;
        uint8 mitm = TRUE;
        uint8 bonding = TRUE;
        uint8 ioCap = GAPBOND_IO_CAP_DISPLAY_ONLY;
        GAPBondMgr_SetParameter( GAPBOND_PERI_DEFAULT_PASSCODE, sizeof ( uint32 ), &passkey );
        GAPBondMgr_SetParameter( GAPBOND_PERI_PAIRING_MODE, sizeof ( uint8 ), &pairMode );
        GAPBondMgr_SetParameter( GAPBOND_PERI_MITM_PROTECTION, sizeof ( uint8 ), &mitm );
        GAPBondMgr_SetParameter( GAPBOND_PERI_IO_CAPABILITIES, sizeof ( uint8 ), &ioCap );
        GAPBondMgr_SetParameter( GAPBOND_PERI_BONDING_ENABLED, sizeof ( uint8 ), &bonding );
    }

    // 初始化GATT属性
    GGS_AddService( GATT_ALL_SERVICES );                      // GAP服务
    GATTServApp_AddService( GATT_ALL_SERVICES );              // GATT属性服务
    DevInfo_AddService();                                     // 设备信息服务
    SimpleProfile_AddService( GATT_ALL_SERVICES );            // 简单GATT配置文件

    // 注册回调函数
    SimpleProfile_RegisterAppCBs( &Peripheral_SimpleProfileCBs );
    GAPRole_BroadcasterSetCB( &Broadcaster_BroadcasterCBs );

    // 启动设备
    tmos_set_event( Peripheral_TaskID, SBP_START_DEVICE_EVT );
}

// BLE事件处理
uint16 Peripheral_ProcessEvent( uint8 task_id, uint16 events )
{
    if ( events & SYS_EVENT_MSG ){
        uint8 *pMsg;
        if ( (pMsg = tmos_msg_receive( Peripheral_TaskID )) != NULL ){
            Peripheral_ProcessTMOSMsg( (tmos_event_hdr_t *)pMsg );
            tmos_msg_deallocate( pMsg );
        }
        return (events ^ SYS_EVENT_MSG);
    }

    if ( events & SBP_START_DEVICE_EVT ){
        // 启动设备
        GAPRole_PeripheralStartDevice( Peripheral_TaskID, &Peripheral_BondMgrCBs, &Peripheral_PeripheralCBs );
        return ( events ^ SBP_START_DEVICE_EVT );
    }

    if ( events & SBP_PERIODIC_EVT ){
        // 重启定时器
        if ( SBP_PERIODIC_EVT_PERIOD ){
            tmos_start_task( Peripheral_TaskID, SBP_PERIODIC_EVT, SBP_PERIODIC_EVT_PERIOD );
        }
        // 执行周期性任务
        performPeriodicTask();
        return (events ^ SBP_PERIODIC_EVT);
    }

    return 0;
}

// 连接状态通知回调
static void peripheralStateNotificationCB( gapRole_States_t newState, gapRoleEvent_t * pEvent)
{
    switch ( newState & GAPROLE_STATE_ADV_MASK )
    {
        case GAPROLE_STARTED:
            PRINT( "Initialized..\n" );
            break;

        case GAPROLE_ADVERTISING:
            if( pEvent->gap.opcode == GAP_MAKE_DISCOVERABLE_DONE_EVENT ){
                PRINT( "Advertising..\n" );
            }
            break;

        case GAPROLE_CONNECTED:
            if( pEvent->gap.opcode == GAP_LINK_ESTABLISHED_EVENT ){
                Peripheral_LinkEstablished( pEvent );
            }
            PRINT( "Connected..\n" );
            break;

        case GAPROLE_CONNECTED_ADV:
            PRINT( "Connected Advertising..\n" );
            break;

        case GAPROLE_WAITING:
            if( pEvent->gap.opcode == GAP_END_DISCOVERABLE_DONE_EVENT ){
                PRINT( "Waiting for advertising..\n" );
            }
            else if( pEvent->gap.opcode == GAP_LINK_TERMINATED_EVENT ){
                Peripheral_LinkTerminated( pEvent );
                PRINT( "Disconnected.. Reason:%x\n",pEvent->linkTerminate.reason );
            }
            break;

        case GAPROLE_ERROR:
            PRINT( "Error..\n" );
            break;

        default:
            break;
    }
}
```

### 8.11 字符串处理工具 (my_string.c)

```c
// 在缓冲区中搜索目标数据
uint8_t * ScanDataInBuffer(uint8_t * buffer, uint16_t buffer_length, uint8_t * data, uint16_t data_length)
{
    uint8_t * scan_result = NULL;

    if(data_length <= buffer_length){
        for(uint16_t buffer_i = 0, data_i = 0; buffer_i < buffer_length; buffer_i++){
            if((*(buffer + buffer_i)) == (*(data + data_i))){
                data_i++;
                if(data_i == data_length){                    // 对比完毕，已经找到
                    scan_result = buffer + buffer_i - data_length + 1;
                    break;
                }
            }
            else{                                             // 不相等
                if(data_i != 0){                              // 之前有相同字符，只是不完全相同
                    data_i = 0;
                    buffer_i -= 1;                            // 继续循环后会+1，这里提前-1，保持位置不变
                }
            }
        }
    }
    return scan_result;
}

// 十六进制字符串转整数
uint32_t HexStringToInt(uint8_t * hex_string, uint8_t string_length)
{
    uint32_t result = 0;
    for(uint8_t i = 0; i < string_length; i++){
        if((hex_string[i]>=0x30)&&(hex_string[i]<=0x39)){     // 0-9的字符
            result += (hex_string[i] - 0x30) * (pow(16,string_length-i-1));
        }
        else if((hex_string[i]>=0x41)&&(hex_string[i]<=0x46)){ // A-F的字符
            result += (hex_string[i] - 0x37) * (pow(16,string_length-i-1));
        }
        else if((hex_string[i]>=0x61)&&(hex_string[i]<=0x66)){ // a-f的字符
            result += (hex_string[i] - 0x57) * (pow(16,string_length-i-1));
        }
        else
            return 0;
    }
    return result;
}

// 数据转十六进制字符串
uint32_t DataToHexString(uint8_t * input_buffer, uint32_t input_length, uint8_t * output_buffer, uint32_t output_buffer_max_length)
{
    if(input_length == 0) return 0;

    uint32_t output_length = input_length * 2;
    if(output_buffer_max_length < output_length){
        PRINT("output buffer size not enough, require %d bytes, but given %d bytes!\r\n",output_length,output_buffer_max_length);
        return 0;
    }

    uint8_t * output_temporary = (uint8_t *)malloc(output_length);

    for(int i = 0; i < input_length; i++){
        sprintf((char *)(output_temporary + i*2),"%02X",input_buffer[i]);
    }

    memset(output_buffer,0,output_buffer_max_length);
    memcpy(output_buffer,output_temporary,output_length);

    free(output_temporary);
    return output_length;
}

// 十六进制字符串转数据
uint32_t HexStringToData(uint8_t * input_hex_string, uint32_t hex_string_length, uint8_t * output_buffer, uint32_t output_buffer_max_length)
{
    if(hex_string_length == 0) return 0;

    if((hex_string_length % 2) != 0){
        PRINT("the input length is illegal, must be an even number!\r\n");
        return 0;
    }

    uint32_t output_length = hex_string_length / 2;

    if(output_buffer_max_length < output_length){
        PRINT("output buffer size not enough, require %d bytes, but given %d bytes!\r\n",output_length,output_buffer_max_length);
        return 0;
    }

    uint8_t * output_temporary = (uint8_t *)malloc(output_length);

    for(int i = 0; i < output_length; i++){
        * (output_temporary+i) = HexStringToInt(input_hex_string + i * 2, 2);
    }

    memset(output_buffer,0,output_buffer_max_length);
    memcpy(output_buffer,output_temporary,output_length);

    free(output_temporary);
    return output_length;
}
```

### 8.12 UART驱动 (user_uart.c)

```c
// UART0初始化 (用于EC800M通信)
void uart0_init(void)
{
    // 配置串口：先配置IO口模式，再配置串口
    GPIOB_SetBits(GPIO_Pin_7);
    GPIOB_ModeCfg(GPIO_Pin_4, GPIO_ModeIN_PU);               // RXD0-配置上拉输入
    GPIOB_ModeCfg(GPIO_Pin_7, GPIO_ModeOut_PP_5mA);          // TXD0-配置推挽输出，注意先让IO口输出高电平
    UART0_DefInit();

    UART0_ByteTrigCfg(UART_7BYTE_TRIG);                      // 串口字节触发中断
    uart0_trig_byte = 7;
    UART0_INTCfg( ENABLE, RB_IER_RECV_RDY );                 // 使能接收中断
    NVIC_EnableIRQ( UART0_IRQn );                            // 使能UART0中断
}

// UART1初始化 (用于调试)
void uart1_init(void)
{
    GPIOA_SetBits(GPIO_Pin_9);
    GPIOA_ModeCfg(GPIO_Pin_8, GPIO_ModeIN_PU);               // RXD1-配置上拉输入
    GPIOA_ModeCfg(GPIO_Pin_9, GPIO_ModeOut_PP_5mA);          // TXD1-配置推挽输出
    UART1_DefInit();

    UART1_ByteTrigCfg(UART_1BYTE_TRIG);                      // 串口字节触发中断
    uart1_trig_byte = 1;
    UART1_INTCfg( ENABLE, RB_IER_RECV_RDY );                 // 使能接收中断
    NVIC_EnableIRQ( UART1_IRQn );                            // 使能UART1中断
}

// UART接收完成检查
void uart_check_rxd_finish(void)
{
    // UART0接收完成检查
    if(uart0_rxd_std == UART_RXD_STD_RXD_ING){
        uart0_rxd_finish_delay_time++;
        if(uart0_rxd_finish_delay_time >= UART_RXD_FINISH_DELAY_TIME){
            uart0_rxd_std = UART_RXD_STD_RXD_FINISHED;
            uart0_rxd_finish_delay_time = 0;
        }
    }

    // UART1接收完成检查
    if(uart1_rxd_std == UART_RXD_STD_RXD_ING){
        uart1_rxd_finish_delay_time++;
        if(uart1_rxd_finish_delay_time >= UART_RXD_FINISH_DELAY_TIME){
            uart1_rxd_std = UART_RXD_STD_RXD_FINISHED;
            uart1_rxd_finish_delay_time = 0;
        }
    }
}

// UART0中断处理函数
void user_uart0_irqhandler(void)
{
    switch( UART0_GetITFlag() ){
        case UART_II_RECV_RDY:                                // 数据达到设置触发点
            for(uint8_t i = 0; i < uart0_trig_byte; i++){
                if(uart0_rxd_index < UART0_RXD_BUF_SIZE){
                    uart0_rxd_buf[uart0_rxd_index] = UART0_RecvByte();
                    uart0_rxd_index++;
                }
                else{
                    UART0_RecvByte();                         // 丢弃数据
                }
            }
            uart0_rxd_std = UART_RXD_STD_RXD_ING;
            uart0_rxd_finish_delay_time = 0;
            break;

        case UART_II_RECV_TOUT:                               // 接收超时，暂停一段时间后
            while(R8_UART0_RFC){
                if(uart0_rxd_index < UART0_RXD_BUF_SIZE){
                    uart0_rxd_buf[uart0_rxd_index] = UART0_RecvByte();
                    uart0_rxd_index++;
                }
                else{
                    UART0_RecvByte();                         // 丢弃数据
                }
            }
            uart0_rxd_std = UART_RXD_STD_RXD_ING;
            uart0_rxd_finish_delay_time = 0;
            break;

        default:
            break;
    }
}
```

### 8.2 触摸屏驱动 (chsc5816.c)

```c
// 触摸点读取
void chsc5816_tp_point_read(uint16_t *tp_x, uint16_t *tp_y, uint8_t *tp_state)
{
    rpt_content_t tp;
    memset(&tp, 0, sizeof(rpt_content_t));

    if(chsc5816_read(0x2000002C, (uint8_t*)&tp, sizeof(rpt_content_t)) == 0){
        if(tp.act == 1 && tp.num > 0){   // 有触摸点
            *tp_x = tp.points[0].rp.x;
            *tp_y = tp.points[0].rp.y;
            *tp_state = 1;               // 触摸状态
        }
        else{
            *tp_state = 0;               // 释放状态
        }
    }
    else{
        *tp_state = 0;
    }
}

// I2C读取函数
uint8_t chsc5816_read(uint32_t addr, uint8_t * buf, uint16_t len)
{
    uint8_t addr_buf[4];
    addr_buf[0] = (addr >> 24) & 0xFF;
    addr_buf[1] = (addr >> 16) & 0xFF;
    addr_buf[2] = (addr >> 8) & 0xFF;
    addr_buf[3] = addr & 0xFF;

    return io_i2c_dev_read(CHSC5816_I2C_ADDR_7BIT, addr_buf[0], 4, buf, len);
}

// 触摸中断处理
void chsc5816_tp_interrupt_handler(void)
{
    if(GPIOB_ReadITFlagBit(GPIO_Pin_15)){
        GPIOB_ClearITFlagBit(GPIO_Pin_15);
        // 触摸中断处理逻辑
        // 可以在这里设置标志位，在主循环中处理触摸事件
    }
}
```

### 8.3 心率血氧传感器驱动 (ob1203.c)

```c
// OB1203读取函数
uint8_t ob1203_read(uint8_t addr, uint8_t *buf, uint8_t len)
{
    return io_i2c_dev_read(OB1203_I2C_ADDR_7BIT, addr, 1, buf, len);
}

// OB1203写入函数
uint8_t ob1203_write(uint8_t addr, uint8_t * buf, uint8_t len)
{
    return io_i2c_dev_write(OB1203_I2C_ADDR_7BIT, addr, 1, buf, len);
}

// 心率血氧初始化
uint8_t ob1203_init(void)
{
    uint8_t init_data;

    // 软复位
    init_data = 0x08;
    if(ob1203_write(0x00, &init_data, 1) != 0) return 1;
    DelayMs(10);

    // 配置LED驱动电流
    init_data = 0x3F;  // 最大电流
    if(ob1203_write(0x15, &init_data, 1) != 0) return 1;

    // 配置采样率
    init_data = 0x00;  // 25Hz采样率
    if(ob1203_write(0x11, &init_data, 1) != 0) return 1;

    // 启动心率模式
    init_data = 0x02;
    if(ob1203_write(0x10, &init_data, 1) != 0) return 1;

    return 0;
}

// 读取心率数据
uint8_t ob1203_read_heart_rate(uint16_t *heart_rate, uint8_t *spo2)
{
    uint8_t status;
    uint8_t data_buf[6];

    // 读取状态寄存器
    if(ob1203_read(0x00, &status, 1) != 0) return 1;

    if(status & 0x01){  // 数据就绪
        // 读取心率和血氧数据
        if(ob1203_read(0x1C, data_buf, 6) != 0) return 1;

        *heart_rate = (data_buf[1] << 8) | data_buf[0];
        *spo2 = data_buf[5];

        return 0;
    }

    return 1;  // 数据未就绪
}
```

### 8.4 振动马达驱动 (vibrat_motor.c)

```c
// 振动马达控制函数
void vibrat_motor_switch(uint8_t switch_v)
{
    switch (switch_v)
    {
        case 0:  // 关闭马达
        {
            GPIOB_ModeCfg(GPIO_Pin_0, GPIO_ModeIN_Floating);   // 配置PB0为浮空输入，默认接3.3V，此时IO为高电平
            // 低电平导通，高电平关闭
        }
        break;

        case 1:  // 打开马达
        {
            GPIOB_ResetBits(GPIO_Pin_0);                       // 将PB0置0（拉低）
            GPIOB_ModeCfg(GPIO_Pin_0, GPIO_ModeOut_PP_5mA);    // 配置为推挽输出，输出0（低电平导通）
        }
        break;

        default:
        break;
    }
}
```

### 8.5 ADC驱动 (user_adc.c)

```c
// ADC通道表定义
adc_channel_table_t sys_adc_channel_table[2] = {
    {
        .average = 0,                                          // ADC数组的均值
        .buffer = adc_channel0_buffer,                         // 存放ADC通道0的数组
        .buffer_index = 0,                                     // ADC的数组数据索引
        .buffer_max_length = sizeof(adc_channel0_buffer)/2,    // 数组的最大长度
        .channel = 0,                                          // 电池电压检测通道
        .sum = 0,
        .sum_length = 0,
        .result_ready = 0,
    },
    {
        .average = 0,                                          // ADC数组的均值
        .buffer = adc_channel6_buffer,                         // 存放ADC通道6的数组
        .buffer_index = 0,
        .buffer_max_length = sizeof(adc_channel6_buffer)/2,
        .channel = 6,                                          // NTC温度传感器通道
        .sum = 0,
        .sum_length = 0,
        .result_ready = 0,
    },
};

// ADC初始化
void user_sys_adc_init(void)
{
    GPIOA_ModeCfg(GPIO_Pin_3, GPIO_ModeIN_Floating);          // NTC-ADC采集
    GPIOA_ModeCfg(GPIO_Pin_4, GPIO_ModeIN_Floating);          // BAT-ADC采集

    // 外部信号单通道采样初始化
    ADC_ExtSingleChSampInit(SampleFreq_3_2, ADC_PGA_1_4);    // 3.2M采样率，-12dB(1/4倍)
    // CH579M参考电压Vref是1.05v(半量程)，默认电压测量范围是0-2.1v
    // PGA增益-12dB(1/4倍)，采样值到电压换算：(ADC/512-3)*Vref，理论可测电压上限：5*Vref
}

// ADC扫描任务
void adc_scan_channel_task(adc_channel_table_t * channel_table, uint8_t table_length)
{
    static uint8_t table_index = 0;
    static uint8_t conver_ing = 0;                            // 标志位，指示当前ADC转换是否正在进行

    if(!conver_ing){
        ADC_ChannelCfg(channel_table[table_index].channel);   // 配置采样通道
        R8_ADC_CONVERT = RB_ADC_START;                        // 开启转换
        conver_ing = 1;                                       // 表示正在转换
    }
    else if(!(R8_ADC_CONVERT & RB_ADC_START)){                // 转换完成
        uint16_t * data_buffer = channel_table[table_index].buffer;
        uint16_t data_index = channel_table[table_index].buffer_index;

        channel_table[table_index].sum -= data_buffer[data_index];     // 从sum中减去旧数据
        data_buffer[data_index] = R16_ADC_DATA & RB_ADC_DATA;          // 读取新的ADC数据
        channel_table[table_index].sum += data_buffer[data_index];     // 加上新数据

        // 更新缓冲区索引
        channel_table[table_index].buffer_index = (data_index + 1) % channel_table[table_index].buffer_max_length;

        // 更新统计信息
        if(channel_table[table_index].sum_length < channel_table[table_index].buffer_max_length){
            channel_table[table_index].sum_length++;
        }

        // 计算平均值和电压
        channel_table[table_index].average = (float)channel_table[table_index].sum / channel_table[table_index].sum_length;
        channel_table[table_index].voltage = (channel_table[table_index].average / 512 - 3) * 1.05f;
        channel_table[table_index].result_ready = 1;

        // 切换到下一个通道
        table_index = (table_index + 1) % table_length;
        conver_ing = 0;
    }
}
```

### 8.6 RTC驱动 (user_rtc.c)

```c
// RTC秒中断开关控制
void urtc_second_irq_switch(uint8_t switch_st)
{
    LClk32K_Select(URTC_CLK_SELECT);                         // 选择32K时钟源
    LClk32k_Power(URTC_CLK_SELECT, switch_st);               // 32K时钟电源控制

#if(URTC_CLK_SELECT == Clk32K_LSI)
    Calibration_LSI();                                        // 对内部32K时钟进行校准
#endif

    RTC_TMRFunCfg(Period_1_S);                               // RTC定时模式配置，1s周期

    if(switch_st){                                            // 如果为1：使能
        NVIC_EnableIRQ(RTC_IRQn);                            // 开启RTC中断
    }
    else{
        NVIC_DisableIRQ(RTC_IRQn);                           // 关闭RTC中断
    }
}
```

### 8.7 定时器延时驱动 (tmdelay.c)

```c
// 全局变量
__IO uint32_t tmdelay_run_time_ms = 0;                       // 毫秒计时器
__IO unix_time_t sys_unix_time = 1691453288u;                // 系统Unix时间戳 (2023-08-08 08:08:08)
uint16_t one_second_ms = 0;                                  // 秒计数器

// 延时定时器初始化
void tmdelay_ms_init(void)
{
    TMR0_TimerInit(FREQ_SYS/1000);                           // 1ms定时器
    TMR0_ITCfg(ENABLE, TMR0_3_IT_CYC_END);                   // 使能定时器中断
    NVIC_EnableIRQ(TMR0_IRQn);                               // 使能中断
}

// 延时时基自增函数 (在毫秒级中断中调用)
void tmdelay_tick_inc()
{
    tmdelay_run_time_ms += 1;                                // 毫秒计数器递增
    one_second_ms += 1;                                      // 秒计数器递增

    if(one_second_ms == 1000){                               // 1秒到达
        one_second_ms = 0;                                   // 重置秒计数器
        sys_unix_time += 1;                                  // Unix时间戳递增
    }
}

// 获取当前毫秒时基
uint32_t get_tmdelay_mstime(void)
{
    return tmdelay_run_time_ms;
}
```

### 8.8 Unix时间处理 (unix_time.c)

```c
// 北京时间转Unix时间戳
unix_time_t get_unix_time(beijing_time_t * beijing_time)
{
    unix_time_t unix_result = 0;
    uint64_t temp_day_number = 0;
    beijing_time_t unix_start_time = { 1970, 1, 1, 8, 0, 0 }; // Unix起始时间的北京时间

    // 计算从起始年到目标年的总天数
    for (uint64_t tempyear = unix_start_time.year; tempyear < beijing_time->year; tempyear++){
        temp_day_number += is_leap_year(tempyear) ? (uint64_t)366 : (uint64_t)365;
    }

    // 计算目标年中从1月到目标月的天数
    date_number_in_month[2] = is_leap_year(beijing_time->year) ? (uint64_t)29 : (uint64_t)28;
    for (uint64_t tempmonth = 1; tempmonth < beijing_time->month; tempmonth++){
        temp_day_number += date_number_in_month[tempmonth];
    }

    temp_day_number += (uint64_t)(beijing_time->day - 1);     // 加上目标月中的天数
    temp_day_number -= (uint64_t)(unix_start_time.day - 1);   // 减去起始天数

    unix_result = (uint64_t)temp_day_number * 60 * 60 * 24;   // 转换为秒
    unix_result += (uint64_t)(beijing_time->hour * 3600 + beijing_time->min * 60 + beijing_time->sec);
    unix_result -= (uint64_t)(unix_start_time.hour * 3600 + unix_start_time.min * 60 + unix_start_time.sec);

    return unix_result;
}

// Unix时间戳转北京时间
void get_beijing_time(beijing_time_t * result, unix_time_t unix_time)
{
    beijing_time_t unix_start_time = { 1970, 1, 1, 8, 0, 0 }; // 北京时间
    unix_time += 28800;                                        // 增加8小时时区偏移
    memset(result, 0, sizeof(beijing_time_t));

    // 计算年
    result->year = unix_start_time.year;
    while (1){
        uint64_t this_year_seconds = is_leap_year(result->year) ? 31622400 : 31536000;
        if(unix_time < this_year_seconds) break;
        unix_time -= this_year_seconds;
        result->year += 1;
    }

    // 计算月
    result->month = 1;
    date_number_in_month[2] = is_leap_year(result->year) ? (uint64_t)29 : (uint64_t)28;
    while(1){
        uint64_t this_month_seconds = date_number_in_month[result->month] * 86400;
        if(unix_time < this_month_seconds) break;
        unix_time -= this_month_seconds;
        result->month += 1;
    }

    // 计算日、时、分、秒
    result->day = unix_time / 86400 + 1;
    unix_time = unix_time % 86400;
    result->hour = unix_time / 3600;
    unix_time = unix_time % 3600;
    result->min = unix_time / 60;
    result->sec = unix_time % 60;
}
```

### 8.9 SPI Flash驱动 (gd25lq32eeigr.c)

```c
// SPI片选控制
void spi_cs(uint8_t bit_statu)
{
    switch (bit_statu)
    {
        case 0:{
            GPIOA_ResetBits(GPIO_Pin_2);    // 片选拉低
            GPIOA_ModeCfg(GPIO_Pin_2, GPIO_ModeOut_PP_5mA);
        }
        break;

        default:{
            GPIOA_ModeCfg(GPIO_Pin_2, GPIO_ModeIN_Floating);  // 片选拉高
        }
        break;
    }
}

// 读取Flash ID
uint32_t spi_flash_read_id(void)
{
    uint32_t flash_id = 0;
    uint8_t id_buf[3];

    spi_cs(0);                           // 片选拉低
    SPI1_MasterSendByte(CMD_READ_ID);    // 发送读ID命令
    id_buf[0] = SPI1_MasterSendByte(0xFF);
    id_buf[1] = SPI1_MasterSendByte(0xFF);
    id_buf[2] = SPI1_MasterSendByte(0xFF);
    spi_cs(1);                           // 片选拉高

    flash_id = (id_buf[0] << 16) | (id_buf[1] << 8) | id_buf[2];
    return flash_id;
}

// 读取Flash数据
void spi_flash_read_data(uint32_t addr, uint8_t * buf, uint16_t len)
{
    spi_cs(0);
    SPI1_MasterSendByte(0x03);           // 读命令
    SPI1_MasterSendByte((addr >> 16) & 0xFF);
    SPI1_MasterSendByte((addr >> 8) & 0xFF);
    SPI1_MasterSendByte(addr & 0xFF);

    for(uint16_t i = 0; i < len; i++){
        buf[i] = SPI1_MasterSendByte(0xFF);
    }

    spi_cs(1);
}

// 写入Flash数据
void spi_flash_write_data(uint32_t addr, uint8_t * buf, uint16_t len)
{
    // 写使能
    spi_cs(0);
    SPI1_MasterSendByte(0x06);           // 写使能命令
    spi_cs(1);

    // 页编程
    spi_cs(0);
    SPI1_MasterSendByte(0x02);           // 页编程命令
    SPI1_MasterSendByte((addr >> 16) & 0xFF);
    SPI1_MasterSendByte((addr >> 8) & 0xFF);
    SPI1_MasterSendByte(addr & 0xFF);

    for(uint16_t i = 0; i < len; i++){
        SPI1_MasterSendByte(buf[i]);
    }

    spi_cs(1);

    // 等待写入完成
    uint8_t status;
    do {
        DelayMs(1);
        spi_cs(0);
        SPI1_MasterSendByte(0x05);       // 读状态寄存器命令
        status = SPI1_MasterSendByte(0xFF);
        spi_cs(1);
    } while(status & 0x01);              // 等待WIP位清零
}
```

## 9. 系统特性总结

### 9.1 核心功能特点

1. **辐射剂量检测**:
   - 基于CSI传感器的γ射线检测
   - 实时剂量率计算和累计剂量统计
   - 智能跳变检测算法，提高测量精度
   - 分段拟合算法，适应不同计数率区间

2. **4G通信功能**:
   - EC800M模块实现4G网络连接
   - MQTT协议数据上传
   - AT状态机管理通信流程
   - 支持GNSS定位功能

3. **用户界面**:
   - 基于LVGL的图形界面
   - 触摸屏交互支持
   - 多界面切换和手势操作
   - 实时数据显示更新

4. **数据管理**:
   - Protobuf协议数据编码
   - SPI Flash数据存储
   - 系统配置管理
   - 数据校验和备份机制

5. **电源管理**:
   - 电池电量监测
   - 充电状态检测
   - 低功耗设计
   - 智能电源管理

### 9.2 技术亮点

1. **跳变检测算法**:
   - 采用3σ统计方法和连续增减法
   - 有效识别辐射环境突变
   - 提高测量数据的可靠性

2. **分段拟合算法**:
   - 针对不同计数率区间使用不同的拟合参数
   - 二次拟合公式: dose_rate = a*x² + b*x + c
   - 提高测量精度和线性度

3. **AT状态机**:
   - 灵活的状态机框架
   - 支持超时重试机制
   - 便于扩展和维护

4. **模块化设计**:
   - 良好的代码结构
   - 各功能模块独立
   - 便于开发和调试

### 9.3 系统架构优势

1. **实时性**:
   - 中断驱动的设计保证了系统的实时响应
   - 1ms定时器中断提供精确时基
   - 秒中断处理剂量率计算

2. **稳定性**:
   - 看门狗机制防止系统死机
   - 错误处理和重试机制
   - 数据校验和备份

3. **可扩展性**:
   - 模块化设计便于功能扩展
   - 标准化的接口设计
   - 支持多种传感器接入

4. **可维护性**:
   - 清晰的代码结构和注释
   - 统一的编程风格
   - 完善的调试接口

### 9.4 完整驱动模块列表

**核心处理模块**:
- main.c - 主程序入口和主循环
- sys_it.c - 中断服务程序
- system_set.c - 系统配置管理

**传感器驱动模块**:
- csi_sensor.c - CSI辐射传感器驱动
- dose_rate_counter.c - 剂量率计数器和跳变检测算法
- jump_averager.c - 跳变检测算法实现
- ob1203.c - 心率血氧传感器驱动
- ntc_temp.c - NTC温度传感器驱动
- user_adc.c - ADC采集驱动

**通信模块**:
- ec800m.c - EC800M 4G通信模块驱动
- at_state_machine.c - AT命令状态机
- user_uart.c - UART串口驱动

**蓝牙模块**:
- peripheral.c - BLE外设主程序
- gattprofile.c - GATT配置文件
- devinfoservice.c - 设备信息服务

**显示和交互模块**:
- pingmu.c - 屏幕总体控制
- icna3311.c - 显示屏驱动
- chsc5816.c - 触摸屏驱动
- lv_port_disp.c - LVGL显示接口移植
- lv_port_indev.c - LVGL输入设备移植
- ui.c - 用户界面管理
- user_display.c - 显示数据更新

**存储和数据处理模块**:
- gd25lq32eeigr.c - SPI Flash驱动
- analytical_pb.c - Protobuf数据编码
- my_string.c - 字符串处理工具

**系统服务模块**:
- battery.c - 电池管理
- userkey.c - 按键处理
- vibrat_motor.c - 振动马达驱动
- user_rtc.c - RTC实时时钟驱动
- tmdelay.c - 定时器延时驱动
- unix_time.c - Unix时间处理
- io_i2c_logic.c - I2C逻辑驱动
- io_i2c_io.c - I2C硬件接口

### 9.5 关键技术参数

**硬件平台**:
- **MCU**: CH579 32位ARM Cortex-M0内核，60MHz主频
- **Flash**: 256KB程序存储器
- **RAM**: 32KB数据存储器
- **外部存储**: 4MB SPI Flash (GD25LQ32EEIGR)

**通信功能**:
- **4G通信**: EC800M Cat.1模块，支持LTE网络
- **蓝牙**: BLE 5.0，支持GATT服务
- **GNSS**: 内置GPS/北斗定位功能
- **协议**: MQTT数据上传，Protobuf数据编码

**显示和交互**:
- **显示屏**: 320x240 TFT LCD (ICNA3311控制器)
- **触摸屏**: 电容触摸屏 (CHSC5816控制器)
- **用户界面**: 基于LVGL图形库
- **按键**: 单个物理按键，支持短按/长按

**传感器系统**:
- **辐射传感器**: CSI γ射线探测器
- **心率血氧**: OB1203光电传感器
- **温度传感器**: NTC热敏电阻
- **电池监测**: ADC电压检测

**电源管理**:
- **电池**: 锂电池，支持磁吸充电
- **电量检测**: 实时电量百分比显示
- **充电检测**: 充电器插入状态检测
- **低功耗**: 支持睡眠模式

**环境参数**:
- **工作温度**: -10°C ~ +60°C
- **存储温度**: -20°C ~ +70°C
- **湿度**: 相对湿度 ≤ 85%
- **防护等级**: IP65 (防尘防水)

### 9.6 软件架构特点

**实时性设计**:
- 1ms定时器中断提供精确时基
- RTC秒中断处理剂量率计算
- 中断驱动的按键和触摸检测
- 看门狗机制保证系统稳定

**模块化架构**:
- 清晰的分层设计
- 标准化的驱动接口
- 独立的功能模块
- 便于扩展和维护

**数据处理算法**:
- 3σ跳变检测算法
- 分段拟合剂量率计算
- 数据平滑和滤波
- 统计学方法提高精度

**通信协议栈**:
- AT命令状态机管理
- MQTT协议数据上传
- Protobuf数据编码
- 错误重试机制

---

**文档说明**: 本文档详细整理了智能手表项目的核心代码模块，涵盖了主程序、驱动程序、通信模块、用户界面、数据协议等所有关键部分。每个模块都提供了详细的代码示例和功能说明，并标注了重要的技术要点，便于理解项目的整体架构和实现细节。该文档可作为项目开发、维护和技术交流的重要参考资料。

